"""
File management utilities for temporary storage and automatic cleanup.
Optimized for memory-constrained environments.
"""

import os
import uuid
import time
import threading
import logging
from pathlib import Path
from typing import Dict, Optional
from config import Config

logger = logging.getLogger(__name__)

class FileManager:
    def __init__(self):
        """Initialize file manager with in-memory tracking."""
        self.audio_files: Dict[str, str] = {}  # ID -> file_path mapping
        self.file_timestamps: Dict[str, float] = {}  # ID -> creation_time mapping
        self.lock = threading.Lock()
        
        # Start cleanup thread
        self.cleanup_thread = threading.Thread(target=self._cleanup_worker, daemon=True)
        self.cleanup_thread.start()
        
        logger.info("FileManager initialized with cleanup worker")
    
    def generate_unique_id(self) -> str:
        """Generate a unique ID for file tracking."""
        return str(uuid.uuid4())
    
    def register_audio_file(self, file_path: str) -> str:
        """Register an audio file and return its unique ID."""
        file_id = self.generate_unique_id()
        
        with self.lock:
            self.audio_files[file_id] = file_path
            self.file_timestamps[file_id] = time.time()
        
        logger.info(f"Registered audio file: {file_id} -> {file_path}")
        return file_id
    
    def get_audio_file_path(self, file_id: str) -> Optional[str]:
        """Get the file path for a given ID."""
        with self.lock:
            return self.audio_files.get(file_id)
    
    def remove_audio_file(self, file_id: str) -> bool:
        """Remove audio file from disk and memory tracking."""
        with self.lock:
            file_path = self.audio_files.get(file_id)
            if not file_path:
                logger.warning(f"Audio file ID not found: {file_id}")
                return False
            
            # Remove from tracking
            del self.audio_files[file_id]
            del self.file_timestamps[file_id]
        
        # Delete physical file
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"Deleted audio file: {file_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to delete audio file {file_path}: {e}")
            return False
    
    def safe_delete_file(self, file_path: str) -> bool:
        """Safely delete a file with error handling."""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"Deleted file: {file_path}")
                return True
            return False
        except Exception as e:
            logger.error(f"Failed to delete file {file_path}: {e}")
            return False
    
    def get_temp_video_path(self, filename: str) -> str:
        """Generate a temporary path for uploaded video file."""
        safe_filename = f"{uuid.uuid4()}_{filename}"
        return os.path.join(Config.UPLOAD_FOLDER, safe_filename)
    
    def get_temp_audio_path(self, video_filename: str) -> str:
        """Generate a temporary path for extracted audio file."""
        base_name = Path(video_filename).stem
        audio_filename = f"{uuid.uuid4()}_{base_name}.{Config.AUDIO_OUTPUT_FORMAT}"
        return os.path.join(Config.AUDIO_FOLDER, audio_filename)
    
    def _cleanup_worker(self):
        """Background worker to clean up orphaned files."""
        while True:
            try:
                time.sleep(Config.CLEANUP_INTERVAL)
                self._cleanup_orphaned_files()
            except Exception as e:
                logger.error(f"Cleanup worker error: {e}")
    
    def _cleanup_orphaned_files(self):
        """Clean up files that are older than the cleanup interval."""
        current_time = time.time()
        orphaned_ids = []
        
        with self.lock:
            for file_id, timestamp in self.file_timestamps.items():
                if current_time - timestamp > Config.CLEANUP_INTERVAL:
                    orphaned_ids.append(file_id)
        
        # Clean up orphaned files
        for file_id in orphaned_ids:
            logger.warning(f"Cleaning up orphaned file: {file_id}")
            self.remove_audio_file(file_id)
        
        # Clean up any remaining files in temp directories
        self._cleanup_temp_directories()
    
    def _cleanup_temp_directories(self):
        """Clean up any remaining files in temporary directories."""
        for directory in [Config.UPLOAD_FOLDER, Config.AUDIO_FOLDER]:
            try:
                if os.path.exists(directory):
                    for file_path in Path(directory).glob('*'):
                        if file_path.is_file():
                            # Check if file is older than cleanup interval
                            if time.time() - file_path.stat().st_mtime > Config.CLEANUP_INTERVAL:
                                file_path.unlink()
                                logger.info(f"Cleaned up orphaned file: {file_path}")
            except Exception as e:
                logger.error(f"Error cleaning up directory {directory}: {e}")
    
    def get_stats(self) -> Dict:
        """Get current file manager statistics."""
        with self.lock:
            return {
                'tracked_files': len(self.audio_files),
                'upload_folder_exists': os.path.exists(Config.UPLOAD_FOLDER),
                'audio_folder_exists': os.path.exists(Config.AUDIO_FOLDER)
            }

# Global file manager instance
file_manager = FileManager()
