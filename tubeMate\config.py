"""
Configuration settings for the video-to-audio extraction service.
Optimized for Render.com free tier (512MB RAM limit).
"""

import os
import tempfile
from pathlib import Path

class Config:
    # Server configuration
    HOST = os.getenv('HOST', '0.0.0.0')
    PORT = int(os.getenv('PORT', 5000))
    DEBUG = os.getenv('DEBUG', 'False').lower() == 'true'
    
    # File handling configuration
    MAX_CONTENT_LENGTH = 100 * 1024 * 1024  # 100MB max file size
    UPLOAD_FOLDER = os.path.join(tempfile.gettempdir(), 'video_uploads')
    AUDIO_FOLDER = os.path.join(tempfile.gettempdir(), 'audio_extracts')
    
    # Supported file formats
    ALLOWED_VIDEO_EXTENSIONS = {
        'mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'webm', 'm4v'
    }
    AUDIO_OUTPUT_FORMAT = 'mp3'
    AUDIO_BITRATE = '128k'  # Balanced quality/size for memory efficiency
    
    # Memory optimization settings
    CLEANUP_INTERVAL = 300  # 5 minutes - cleanup orphaned files
    MAX_CONCURRENT_PROCESSES = 2  # Limit concurrent audio extractions
    STREAM_CHUNK_SIZE = 8192  # 8KB chunks for file streaming
    
    # FFmpeg settings for memory efficiency
    FFMPEG_ARGS = [
        '-y',  # Overwrite output files
        '-loglevel', 'error',  # Minimal logging
        '-threads', '1',  # Single thread to save memory
        '-preset', 'ultrafast',  # Fast processing, less memory
    ]
    
    @staticmethod
    def init_directories():
        """Create necessary directories if they don't exist."""
        Path(Config.UPLOAD_FOLDER).mkdir(parents=True, exist_ok=True)
        Path(Config.AUDIO_FOLDER).mkdir(parents=True, exist_ok=True)
    
    @staticmethod
    def is_allowed_file(filename):
        """Check if the uploaded file has an allowed extension."""
        return ('.' in filename and 
                filename.rsplit('.', 1)[1].lower() in Config.ALLOWED_VIDEO_EXTENSIONS)
